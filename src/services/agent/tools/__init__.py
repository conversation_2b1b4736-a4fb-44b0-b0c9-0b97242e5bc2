"""
Shared tools used by different bot types.
"""

# 导入所有工具模块以确保工具函数被注册
from src.services.agent.tools.data_tools import (
    fetch_mysql_sql_result,
    fetch_odps_sql_result,
    get_sales_manager_team_members,
    get_table_sample_data,
    get_large_areas,
    get_new_customers_of_today,
    get_high_valued_customers_starting_from_now,
    get_sku_cost_and_price_info,
)
from src.services.agent.tools.product_search_tool import search_product_by_name, ask_user_to_confirm_sku
from src.services.agent.tools.ddl_tools import fetch_ddl_for_table
from src.services.agent.tools.feishu_tools import upload_sql_result_to_feishu_if_needed
from src.services.agent.tools.feishu_docs_tool import search_feishu_docs, get_feishu_doc_content_tool
from src.services.agent.tools.feishu_group_chat_content_tool import search_feishu_group_chat_content, get_feishu_message_content_tool
from src.services.agent.tools.api_llms_read_tools import read_api_llms
from src.services.agent.tools.api_call_tools import api_call_tool
from src.services.agent.tools.wiki_tool import search_docs_by_text
from src.services.agent.tools.tool_manager import tool_manager
from src.services.agent.tools.sku_price_tool import sku_price_tool
from src.services.agent.tools.poi_tools import get_user_location, search_poi_by_keyword, search_nearby_customers
from src.services.agent.tools.supplier_call_tools import query_order_item_trace_info, query_warehouse_in_transit_inventory, query_product_supply_warehouse, query_product_certificate_reports, query_warehouse_supply_areas
from src.services.agent.tools.summerfarm_product_sales_tool import query_product_realtime_sales_quantity_in_warehouse

# 导出所有工具函数，使它们可以从tools包中直接导入
__all__ = [
    'fetch_mysql_sql_result',
    'fetch_odps_sql_result',
    'get_sales_manager_team_members',
    'get_table_sample_data',
    'get_large_areas',
    'get_new_customers_of_today',
    'get_high_valued_customers_starting_from_now',
    'get_sku_cost_and_price_info',
    'search_product_by_name',
    'ask_user_to_confirm_sku',
    'fetch_ddl_for_table',
    'upload_sql_result_to_feishu_if_needed',
    'search_feishu_docs',
    'get_feishu_doc_content_tool',
    'search_feishu_group_chat_content',
    'get_feishu_message_content_tool',
    'read_api_llms',
    'api_call_tool',
    'tool_manager',
    'search_docs_by_text',
    'sku_price_tool',
    'get_user_location',
    'search_poi_by_keyword',
    'search_nearby_customers',
    'query_order_item_trace_info',
    'query_warehouse_in_transit_inventory',
    'query_product_supply_warehouse',
    'query_warehouse_supply_areas',
    'query_product_certificate_reports',
    'query_product_realtime_sales_quantity_in_warehouse',
]
