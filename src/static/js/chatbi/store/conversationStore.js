/**
 * 会话与消息的集中式 Store
 */

import { reactive, computed } from 'vue';
import { usePollingManager } from '../composables/usePollingManager.js';
import { renderMarkdown, renderMarkdownLite } from '../../utils/MarkdownRenderer.js';
import { useMessageFormatter } from '../composables/useMessageFormatter.js';
import { deleteConversation as apiDeleteConversation } from '../services/historyService.js';

// 单例轮询管理器（即使不在组件内使用，onUnmounted 钩子失效也不影响核心功能）
const polling = usePollingManager();
const loadingHistoryPages = new Set(); // 防抖：避免同一页并发/重复加载

// 调试开关：localStorage.setItem('debug:chat', '1')
const debugEnabled = (() => {
  try { return window && window.localStorage && window.localStorage.getItem('debug:chat') === '1'; } catch (_) { return false; }
})();
const debug = (...args) => { if (debugEnabled) console.log('[conversationStore]', ...args); };

const state = reactive({
  conversations: new Map(),          
  messagesByCid: new Map(),          
  inflightByChatId: new Map(),       
  activeCid: null,
  isLoading: false,
  error: null,
  // 分页（用于历史侧栏）
  currentPage: 0,
  pageSize: 20,
  totalCount: 0,
  // 历史加载专用状态（与聊天区加载分离，避免互相干扰）
  isHistoryLoading: false,
  historyError: null,
  // 分享对话（模态框）
  isShareModalOpen: false,
  shareUrl: '',
  isGeneratingShareLink: false,
});

/**
 * 选择当前会话
 */
function select(cid) {
  state.activeCid = cid || null;
  debug('select', { cid: state.activeCid });
}

/**
 * 从 URL 读取 chat 参数并返回 { success, conversationId|null }
 */
function loadFromUrl() {
  try {
    const params = new URLSearchParams(window.location.search);
    const chat = params.get('chat');
    if (chat) {
      debug('loadFromUrl', { chat });
      return { success: true, conversationId: chat };
    }
  } catch (_) {}
  return { success: false, conversationId: null };
}

// ===== 内部工具与主流程 =====
function createUiId(prefix = 'msg') { return `${prefix}-${Date.now()}-${Math.random().toString(36).slice(2, 9)}`; }
function ensureMessagesArray(cid) { if (!state.messagesByCid.has(cid)) state.messagesByCid.set(cid, []); return state.messagesByCid.get(cid); }
function ensureConversation(cid, title, lastTs) {
  const conv = state.conversations.get(cid) || { id: cid, title: title || '未命名对话', lastTs: lastTs || Date.now() };
  if (title) conv.title = title;
  if (lastTs) conv.lastTs = lastTs;
  state.conversations.set(cid, conv);
}
function migrateMessages(tempCid, realCid) {
  if (tempCid === realCid) return;
  const arr = state.messagesByCid.get(tempCid) || [];
  if (!state.messagesByCid.has(realCid)) state.messagesByCid.set(realCid, []);
  const target = state.messagesByCid.get(realCid);
  target.push(...arr);
  state.messagesByCid.delete(tempCid);
  // 移除临时会话元信息，避免侧栏出现重复的 pending 项
  if (state.conversations.has(tempCid)) state.conversations.delete(tempCid);
  debug('migrateMessages', { from: tempCid, to: realCid, count: arr.length });
}

// 计算消息数组的最后时间戳，避免在多个函数中重复逻辑
function calculateLastTs(msgs) {
  if (!Array.isArray(msgs) || msgs.length === 0) {
    return Date.now();
  }
  const toUnixMs = (m) => {
    // 优先使用数值型 timestamp
    if (m && typeof m.timestamp !== 'undefined') {
      const n = Number(m.timestamp);
      if (!Number.isNaN(n) && Number.isFinite(n) && n > 0) return n;
    }
    // 兜底：解析 updated_at 字段（可能为 'YYYY-MM-DD HH:mm:ss' 或 ISO 字符串）
    if (m && m.updated_at) {
      const s = typeof m.updated_at === 'string' ? m.updated_at.replace(' ', 'T') : m.updated_at;
      const t = new Date(s).getTime();
      if (!Number.isNaN(t)) return t;
    }
    return 0;
  };
  const lastTs = msgs.reduce((acc, m) => Math.max(acc, toUnixMs(m)), 0);
  return (!lastTs || Number.isNaN(lastTs)) ? Date.now() : lastTs;
}

/**
 * 恢复未完成消息的轮询（页面刷新/切页后）
 *
 * 规则：
 * - 仅处理 role=assistant 且 isInProcess/isStreaming 为 true 的消息
 * - 需要存在有效的 chatHistoryId（数字）
 * - 通过 inflightByChatId 建立 chat_history_id -> { cid, uiId } 的映射
 * - 使用轮询管理器按 chat_history_id 重新开始轮询，并在 onUpdate/onComplete 中就地更新消息对象
 */
function resumePollingForUnfinishedMessages(cid, messages) {
  try {
    const list = Array.isArray(messages) ? messages : [];
    list.forEach((m) => {
      if (!m || m.role !== 'assistant') return;
      const inProcess = m.isInProcess === true || m.isStreaming === true || m.is_in_process === 1;
      const hid = typeof m.chatHistoryId === 'string' ? parseInt(m.chatHistoryId) : m.chatHistoryId;
      if (!inProcess || !hid || Number.isNaN(hid)) return;

      // 若已存在映射/轮询，跳过重复注册
      if (state.inflightByChatId.has(hid)) return;
      try { if (polling && typeof polling.isPolling === 'function' && polling.isPolling(hid)) return; } catch (_) {}

      const uiId = m.uiId || m.id; // 历史消息通常没有 uiId，这里统一用 id 兜底
      state.inflightByChatId.set(hid, { cid, uiId });
      debug('resumePolling', { cid, uiId, chatHistoryId: hid });

      // 启动轮询并就地更新目标消息
      polling.startPolling(hid, {
        onUpdate: (d) => {
          const map = state.inflightByChatId.get(hid); if (!map) return;
          const msgs = state.messagesByCid.get(map.cid) || [];
          // 先按 uiId 命中，否则退化按 chatHistoryId 命中
          const target = msgs.find(x => (x.uiId === map.uiId) || (x.id === map.uiId) || (x.chatHistoryId === hid));
          if (!target) return;
          target.content = d.content || '';
          target.renderedContent = renderMarkdownLite(target.content);
          target.isStreaming = true;
          target.isInProcess = true;
          target.chatHistoryId = hid;
          ensureConversation(map.cid, null, Date.now());
        },
        onComplete: (d) => {
          const map = state.inflightByChatId.get(hid);
          if (map) {
            const msgs = state.messagesByCid.get(map.cid) || [];
            const target = msgs.find(x => (x.uiId === map.uiId) || (x.id === map.uiId) || (x.chatHistoryId === hid));
            if (target) {
              target.content = d.content || '';
              target.renderedContent = renderMarkdown(target.content);
              target.isStreaming = false;
              target.isInProcess = false;
              // 使用 Unix 毫秒时间戳，避免多种格式导致计算复杂
              target.timestamp = Date.now();
              target.chatHistoryId = hid;
              if (typeof d.timeSpend !== 'undefined') target.time_spend = d.timeSpend;
            }
            ensureConversation(map.cid, null, Date.now());
          }
          state.inflightByChatId.delete(hid);
        },
        onError: (e) => {
          console.error('[conversationStore] 恢复轮询时出错', e);
          state.inflightByChatId.delete(hid);
        }
      });
    });
  } catch (e) {
    console.warn('[conversationStore] resumePollingForUnfinishedMessages 失败:', e?.message || e);
  }
}

/**
 * 发送用户查询（统一入口）：
 * - 立即在本地插入用户消息 + AI 占位（如无活动cid则创建临时cid）
 * - 请求 /query 拿到真实 conversation_id 与 chat_history_id
 * - 迁移临时cid → 真实cid，注册 inflight 映射，并用轮询回调直接更新消息
 */
async function send({ text, images = [], agent = null, conversationId = null, uiId = null }) {
  // 选择工作中的 cid：优先参数，再用活动cid，否则创建临时cid
  let workingCid = conversationId || state.activeCid;
  if (!workingCid) {
    workingCid = `pending-${(crypto?.randomUUID ? crypto.randomUUID() : createUiId('cid'))}`;
    state.activeCid = workingCid;
  }

  // 1) 追加用户消息
  const userId = createUiId('user');
  const arr = ensureMessagesArray(workingCid);
  arr.push({ uiId: userId, role: 'user', content: text, images: images || [], timestamp: Date.now() });

  // 2) 追加AI占位
  const aiId = uiId || createUiId('ai');
  arr.push({ uiId: aiId, role: 'assistant', content: '', renderedContent: '', isStreaming: true, isInProcess: true, timestamp: Date.now() });
  ensureConversation(workingCid, text.length > 30 ? text.slice(0, 30) + '...' : text, Date.now());

  // 3) 拼接请求体
  const payload = { query: text, timestamp: Date.now() };
  if (images && images.length > 0) payload.images = images;
  if (agent) payload.agent = agent;
  if (conversationId) payload.conversation_id = conversationId;
  else if (state.activeCid && !state.activeCid.startsWith('pending-')) payload.conversation_id = state.activeCid;

  try {
    // 注入用户信息
    try { if (window && window.userInfo) payload.user_info = { name: window.userInfo.name || '访客', location: window.userInfo.location || null }; } catch(_){}

    debug('POST /query', { hasImages: images?.length > 0, agent, workingCid, aiId });
    state.isLoading = true; state.error = null;

    const resp = await fetch('/query', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
    if (!resp.ok) {
      let msg = `请求失败: ${resp.status}`;
      try { const err = await resp.json(); if (err && err.error) msg = err.error; } catch(_){}
      throw new Error(msg);
    }
    const data = await resp.json();
    let realCid = data.conversation_id;
    const chatHistoryId = data.chat_history_id || null;

    if (workingCid !== realCid) {
      migrateMessages(workingCid, realCid);
      state.activeCid = realCid;
      workingCid = realCid;
    }
    ensureConversation(realCid, text.length > 30 ? text.slice(0, 30) + '...' : text, Date.now());

    // 注册 inflight 并启动轮询，直接更新 Store 中的占位消息
    if (chatHistoryId) {
      state.inflightByChatId.set(chatHistoryId, { cid: realCid, uiId: aiId });
      polling.startPolling(chatHistoryId, {
        onUpdate: (d) => {
          const map = state.inflightByChatId.get(chatHistoryId); if (!map) return;
          const msgs = state.messagesByCid.get(map.cid) || []; const m = msgs.find(x => x.uiId === map.uiId); if (!m) return;
          m.content = d.content || ''; m.renderedContent = renderMarkdownLite(m.content); m.isStreaming = true; m.isInProcess = true; m.chatHistoryId = chatHistoryId;
          // 更新会话的最后活跃时间，确保侧栏排序即时置顶
          ensureConversation(map.cid, null, Date.now());
        },
        onComplete: (d) => {
          const map = state.inflightByChatId.get(chatHistoryId); if (map) {
            const msgs = state.messagesByCid.get(map.cid) || []; const m = msgs.find(x => x.uiId === map.uiId);
            if (m) { m.content = d.content || ''; m.renderedContent = renderMarkdown(m.content); m.isStreaming = false; m.isInProcess = false; m.timestamp = Date.now(); m.chatHistoryId = chatHistoryId; }
          }
          // 结束时再刷新一次排序时间
          if (map && map.cid) ensureConversation(map.cid, null, Date.now());
          state.inflightByChatId.delete(chatHistoryId);
        },
        onError: (e) => { console.error('[conversationStore] 轮询错误', e); state.inflightByChatId.delete(chatHistoryId); }
      });
    }

    return { conversationId: workingCid, chatHistoryId };
  } catch (error) {
    state.error = error.message || '发送失败';
    // 请求失败时移除 AI 占位消息，避免界面卡在处理中状态
    try {
      const arr = state.messagesByCid.get(workingCid) || [];
      const idx = arr.findIndex(m => m.uiId === aiId);
      if (idx >= 0) arr.splice(idx, 1);
    } catch (_) {}
    throw error;
  } finally {
    state.isLoading = false;
  }
}

export const conversationStore = {
  state,
  select,
  loadFromUrl,
  send,
  /**
   * 加载历史列表（分页）
   */
  async loadHistory(page = 1, limit = 20) {
    if (loadingHistoryPages.has(page)) return { total: state.totalCount };
    try {
      loadingHistoryPages.add(page);
      state.isHistoryLoading = true; state.historyError = null;
      const offset = (page - 1) * limit;
      const resp = await fetch(`/api/history?limit=${limit}&offset=${offset}`);
      if (!resp.ok) {
        let msg = `获取历史失败: ${resp.status}`;
        try { const err = await resp.json(); if (err?.error) msg = err.error; } catch(_){}
        throw new Error(msg);
      }
      const data = await resp.json();
      const history = data.history || {};
      state.totalCount = data.total_count || 0;
      state.currentPage = page; state.pageSize = limit;

      // 规范化写入
      const formatter = useMessageFormatter();
      Object.entries(history).forEach(([cid, msgs]) => {
        // 格式化消息
        const formatted = Array.isArray(msgs) ? msgs.map(formatter.formatMessage) : [];
        // 根据 chatHistoryId 或 uiId 去重合并
        const existing = state.messagesByCid.get(cid) || [];
        const finalMessages = new Map();
        const addOrUpdate = (msg) => {
          const key = msg.chatHistoryId || msg.uiId;
          if (key) finalMessages.set(key, msg);
        };
        existing.forEach(addOrUpdate);
        formatted.forEach(addOrUpdate);
        const merged = Array.from(finalMessages.values());
        state.messagesByCid.set(cid, merged);

        // 更新会话元信息（标题与最后时间戳）
        let title = '未命名对话';
        for (const m of formatted) {
          if (m.role === 'user' && m.content) {
            title = m.content.length > 30 ? m.content.slice(0,30)+'...' : m.content;
            break;
          }
        }
        const lastTs = calculateLastTs(msgs);
        ensureConversation(cid, title, lastTs);

        // 恢复该会话内未完成消息的轮询（页面刷新/翻页后）
        resumePollingForUnfinishedMessages(cid, merged);
      });

      return { total: state.totalCount };
    } catch (e) {
      state.historyError = e.message || '获取历史失败';
      throw e;
    } finally { state.isHistoryLoading = false; loadingHistoryPages.delete(page); }
  },
  hasMorePages: computed(() => state.currentPage * state.pageSize < state.totalCount),
  /**
   * 加载单个对话（用于 URL 直接打开）
   */
  async loadConversation(cid) {
    if (!cid) return;
    try {
      state.isHistoryLoading = true; state.historyError = null;
      const resp = await fetch(`/api/history?chat=${cid}`);
      if (!resp.ok) {
        let msg = `获取对话失败: ${resp.status}`;
        try { const err = await resp.json(); if (err?.error) msg = err.error; } catch(_){}
        throw new Error(msg);
      }
      const data = await resp.json();
      const msgs = Array.isArray(data.conversation) ? data.conversation : [];
      const formatter = useMessageFormatter();
      const formatted = msgs.map(formatter.formatMessage);
      // 采用 upsert 策略合并本地与远端消息
      const existing = state.messagesByCid.get(cid) || [];
      const finalMessages = new Map();
      const addOrUpdate = (msg) => {
        const key = msg.chatHistoryId || msg.uiId;
        if (key) finalMessages.set(key, msg);
      };
      existing.forEach(addOrUpdate);
      formatted.forEach(addOrUpdate);
      state.messagesByCid.set(cid, Array.from(finalMessages.values()));
      let title = '未命名对话';
      for (const m of formatted) { if (m.role==='user' && m.content) { title = m.content.length>30? m.content.slice(0,30)+'...' : m.content; break; } }
      const lastTs = calculateLastTs(msgs);
      ensureConversation(cid, title, lastTs);
      state.activeCid = cid;

      // 单聊打开时，同步恢复未完成消息的轮询
      resumePollingForUnfinishedMessages(cid, formatted);
    } catch (e) {
      state.historyError = e.message || '获取对话失败';
      throw e;
    } finally { state.isHistoryLoading = false; }
  },
  /**
   * 注册正在轮询的消息映射（chat_history_id -> { cid, uiId }）。
   * 说明：里程碑1仅记录映射与打印日志；后续里程碑用于精准更新消息。
   */
  registerInflight(chatHistoryId, cid, uiId) {
    if (!chatHistoryId) return;
    state.inflightByChatId.set(chatHistoryId, { cid, uiId });
    debug('registerInflight', { chatHistoryId, cid, uiId });
  },
  /**
   * 取消注册映射。
   */
  unregisterInflight(chatHistoryId) {
    if (!chatHistoryId) return;
    state.inflightByChatId.delete(chatHistoryId);
    debug('unregisterInflight', { chatHistoryId });
  },
  /**
   * 停止指定消息的轮询
   */
  stopPolling(chatHistoryId) {
    try { polling.stopPolling(chatHistoryId); } catch (e) { console.warn('[conversationStore] 停止轮询失败', e); }
  },
  /**
   * 获取映射（调试/后续使用）。
   */
  getInflight(chatHistoryId) {
    return state.inflightByChatId.get(chatHistoryId) || null;
  },
  /**
   * 轮询更新回调。
   */
  handlePollUpdate(chatHistoryId, data) {
    const map = state.inflightByChatId.get(chatHistoryId);
    debug('pollUpdate', { chatHistoryId, map, len: (data?.content||'').length });
  },
  /**
   * 轮询完成回调。
   */
  handlePollComplete(chatHistoryId, data) {
    const map = state.inflightByChatId.get(chatHistoryId);
    debug('pollComplete', { chatHistoryId, map, len: (data?.content||'').length });
    state.inflightByChatId.delete(chatHistoryId);
  },
  /** 将进行中的消息标记为中断 */
  markInterruptedByHistoryId(chatHistoryId) {
    const map = state.inflightByChatId.get(chatHistoryId);
    if (!map) return;
    const { cid, uiId } = map;
    const arr = state.messagesByCid.get(cid) || [];
    const msg = arr.find(m => m.uiId === uiId);
    if (msg) {
      if (!msg.content || !/\*回复已被中断\*/.test(msg.content)) msg.content = (msg.content || '') + '\n\n*回复已被中断*';
      msg.renderedContent = renderMarkdown(msg.content);
      msg.isStreaming = false; msg.isInProcess = false; msg.isInterrupted = true;
      // 统一使用毫秒时间戳
      msg.timestamp = Date.now();
    }
    state.inflightByChatId.delete(chatHistoryId);
  },
  // 派生：当前活动会话的消息
  activeMessages: computed(() => state.messagesByCid.get(state.activeCid) || []),
  conversationGroups: computed(() => {
    // 基于 conversations 生成分组视图（只读派生）
    const todayStr = new Date().toISOString().split('T')[0];
    const getDateBefore = (days) => { const d=new Date(); d.setDate(d.getDate()-days); return d.toISOString().split('T')[0]; };
    const yesterdayStr = getDateBefore(1);
    const groups = [
      { id: 'today', title: '今天', conversations: [] },
      { id: 'yesterday', title: '昨天', conversations: [] },
      { id: 'past_week', title: '过去 7 天', conversations: [] },
      { id: 'past_month', title: '过去 30 天', conversations: [] },
      { id: 'older', title: '更早', conversations: [] },
    ];
    // 转数组并按 lastTs 降序
    const list = Array.from(state.conversations.values()).sort((a,b)=> (b.lastTs||0)-(a.lastTs||0));
    list.forEach(conv => {
      const dateStr = new Date(conv.lastTs || Date.now()).toISOString().split('T')[0];
      const daysDiff = Math.floor((new Date(todayStr) - new Date(dateStr))/(1000*60*60*24));
      const item = { id: conv.id, title: conv.title, timestamp: conv.lastTs, active: conv.id === state.activeCid };
      if (dateStr === todayStr) groups[0].conversations.push(item);
      else if (dateStr === yesterdayStr) groups[1].conversations.push(item);
      else if (daysDiff <= 7) groups[2].conversations.push(item);
      else if (daysDiff <= 30) groups[3].conversations.push(item);
      else groups[4].conversations.push(item);
    });
    // 组内降序排序（只按时间，不做点击置顶）
    const sorted = groups
      .filter(g => g.conversations.length > 0)
      .map(g => ({ ...g, conversations: g.conversations.sort((a,b)=> (b.timestamp||0)-(a.timestamp||0)) }));
    return sorted;
  }),
  /**
   * 生成分享链接并打开分享模态框
   */
  async shareConversation(conversationId) {
    if (!conversationId) {
      console.error('[conversationStore] 无法分享：会话ID为空');
      return;
    }
    try {
      state.isGeneratingShareLink = true;
      state.isShareModalOpen = true;
      state.shareUrl = '';

      const resp = await fetch('/api/share_conversation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ conversation_id: conversationId }),
      });
      if (!resp.ok) throw new Error(`分享失败: ${resp.status}`);
      const data = await resp.json();
      if (data.share_url) {
        state.shareUrl = data.share_url;
      } else {
        throw new Error('服务器未返回有效的分享链接');
      }
    } catch (e) {
      console.error('[conversationStore] 分享会话失败', e);
      alert(`分享失败: ${e.message}`);
      state.isShareModalOpen = false;
    } finally {
      state.isGeneratingShareLink = false;
    }
  },
  closeShareModal() { state.isShareModalOpen = false; },
  /**
   * 删除对话（带二次确认模态）
   */
  openDeleteConfirmModal(conversationId, title) {
    if (!state.deleteConfirmModal) state.deleteConfirmModal = { isOpen: false, conversationId: null, title: '' };
    state.deleteConfirmModal.isOpen = true;
    state.deleteConfirmModal.conversationId = conversationId;
    state.deleteConfirmModal.title = title || '此对话';
  },
  closeDeleteConfirmModal() {
    if (state.deleteConfirmModal) { state.deleteConfirmModal.isOpen = false; }
  },
  async confirmDeleteConversation() {
    try {
      const cid = state?.deleteConfirmModal?.conversationId;
      if (!cid) return;
      await apiDeleteConversation(cid);
      // 本地状态移除
      if (state.messagesByCid.has(cid)) state.messagesByCid.delete(cid);
      if (state.conversations.has(cid)) state.conversations.delete(cid);
      // 如果删除的是活动会话，清空并移除URL参数
      // URL 的 ?chat= 同步交由布局层统一监听处理（ChatbiLayout.watch(activeCid)）。
      // 这里仅更新状态，遵循“单一职责”和 DRY 原则。
      if (state.activeCid === cid) {
        state.activeCid = null;
      }
    } catch (e) {
      console.error('[conversationStore] 删除会话失败', e);
      alert('删除会话失败');
    } finally {
      if (state.deleteConfirmModal) state.deleteConfirmModal.isOpen = false;
    }
  },
};
