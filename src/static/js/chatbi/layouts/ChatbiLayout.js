import {reactive, ref, onMounted, nextTick, watch, computed} from 'vue';
import ChatHeader from '../components/ChatbiHeader.js';
import HistorySidebar from '../components/HistorySidebar.js';
import ChatContent from '../components/ChatContent.js';
import DeleteConfirmModal from '../components/modals/DeleteConfirmModal.js';
import ShareModal from '../components/modals/ShareModal.js';
import DevLogPanel from '../components/DevLogPanel.js';
import {zLayoutHeader, zLayoutSidebar} from '../../utils/zIndex.js';
import {useLayoutState} from '../composables/useLayoutState.js';
import {useScrollManager} from '../composables/useScrollManager.js';
import ContentContainer from '../../common/layouts/ContentContainer.js';
import { conversationStore } from '../store/conversationStore.js';

export default {
    name: 'AppLayout',
    components: {
        ChatHeader,
        HistorySidebar,
        ChatContent,
        DeleteConfirmModal,
        ShareModal,
        DevLogPanel,
        ContentContainer
    },
    setup() {
        // 获取用户信息
        const userInfo = reactive(window.userInfo || {
            name: '访客',
            avatar: '',
            isAdmin: false
        });

        // 使用布局状态组合式API
        const layoutState = useLayoutState();

        // 移除对 useChatState 的依赖，Chat 逻辑由 Store 驱动

        // 使用滚动管理器组合式API
        const scrollManager = useScrollManager();

        // 获取用户位置信息
        const getUserLocation = async () => {
            try {
                // 检查浏览器是否支持地理位置API
                if (!navigator.geolocation) {
                    console.log('浏览器不支持地理位置功能');
                    return;
                }

                // 获取位置信息
                const position = await new Promise((resolve, reject) => {
                    navigator.geolocation.getCurrentPosition(
                        resolve,
                        reject,
                        {
                            enableHighAccuracy: true,
                            timeout: 10000,
                            maximumAge: 300000 // 5分钟缓存
                        }
                    );
                });

                const { latitude, longitude, accuracy } = position.coords;

                // 将位置信息存储到用户信息中
                userInfo.location = {
                    latitude: latitude,
                    longitude: longitude,
                    accuracy: accuracy,
                    timestamp: Date.now()
                };

                // 同步到全局window.userInfo
                if (!window.userInfo) {
                    window.userInfo = {};
                }
                window.userInfo.location = userInfo.location;

                console.log('成功获取用户位置:', userInfo.location);

            } catch (error) {
                console.log('获取位置失败:', error.message);
                // 用户拒绝或其他错误，静默处理，不影响应用使用
            }
        };

        // 在组件挂载时恢复日志面板状态并从URL加载对话
        // Restore log panel state and load conversation from URL when component is mounted
        onMounted(async () => {
            restoreDevLogState();
            // 从URL加载对话（改用 Store）
            const urlLoad = conversationStore.loadFromUrl();
            if (urlLoad && urlLoad.success && urlLoad.conversationId) {
                try {
                    await conversationStore.loadConversation(urlLoad.conversationId);
                    await nextTick();
                    conversationStore.select(urlLoad.conversationId);
                } catch (e) {
                    console.warn('URL对话加载失败，将保持欢迎态:', e);
                    conversationStore.select(null);
                }
            }
            
            // 获取用户位置
            getUserLocation();
        });

        // 已切换为 Store 驱动，不再监听旧的 historyState

        // 开发日志面板状态
        const isDevLogVisible = ref(false);
        // 暂不接入日志Store，保持空串（后续里程碑接入）
        const devLogs = '';

        // 切换开发日志面板显示
        const toggleDevLog = () => {
            isDevLogVisible.value = !isDevLogVisible.value;
            // 保存状态到 localStorage，确保视图切换时保持状态
            localStorage.setItem('devLogVisible', isDevLogVisible.value ? 'true' : 'false');
        };

        // 从 localStorage 恢复日志面板状态
        const restoreDevLogState = () => {
            const savedState = localStorage.getItem('devLogVisible');
            if (savedState === 'true') {
                isDevLogVisible.value = true;
            }
        };

        // 消息流处理函数，包含日志处理和自动滚动
        const handleAiMessageStreamWithLogs = () => {};

        // 用户消息处理函数，包含日志重置和自动滚动
        const handleUserMessageWithLogReset = () => {
            // 发送前后均滚到底部，确保体验
            setTimeout(() => {
                scrollManager.smartScrollToBottom();
            }, 50);
        };

        // 消息完成处理函数，包含日志处理和自动滚动
        const handleAiMessageCompleteWithLogs = () => {};

        // 简化后的选择对话函数
        const handleNewConversation = () => {
            // 清空活动会话，让发送时由后端生成ID（Store会创建临时ID以显示占位）
            conversationStore.select(null);
            // 如果是移动端，则关闭侧边栏
            if (!layoutState.isDesktopView.value && layoutState.sidebarOpen.value) {
                layoutState.toggleSidebar();
            }
        };

        // 简化后的选择对话函数
        const handleSelectConversation = (conversationId) => {
            // 统一使用 Store 管理活动会话
            try { conversationStore.select(conversationId); } catch (_) {}

            // 如果是移动端，则关闭侧边栏
            if (!layoutState.isDesktopView.value && layoutState.sidebarOpen.value) {
                layoutState.toggleSidebar();
            }

            // 直接定位到底部，不使用动画和复杂的滚动逻辑
            setTimeout(() => {
                const container = scrollManager.getActiveScrollContainer();
                if (container) {
                    // 直接设置滚动位置到底部，不使用scrollTo方法以避免任何动画
                    container.scrollTop = container.scrollHeight;
                }
            }, 100);
        };

        // ---- URL 同步（集中式）----
        // 说明：统一在布局层监听 activeCid 的变化来同步 URL 的 `?chat=` 参数，避免在各处分散维护。
        // 规则：
        // - 当 activeCid 为真实ID（且非以 pending- 开头）时，写入 ?chat=<id>
        // - 当 activeCid 为空或为 pending-* 时，从 URL 移除 chat 参数
        // - 若目标状态与当前 URL 一致，则不调用 history.replaceState，避免无谓的历史记录修改
        const updateUrlChatParam = (cid) => {
            try {
                const url = new URL(window.location.href);
                const current = url.searchParams.get('chat');
                const isPending = typeof cid === 'string' && cid.startsWith('pending-');
                if (!cid || isPending) {
                    if (current !== null) {
                        url.searchParams.delete('chat');
                        window.history.replaceState({}, '', url);
                    }
                    return;
                }
                // 仅当不同才更新
                if (current !== cid) {
                    url.searchParams.set('chat', cid);
                    window.history.replaceState({}, '', url);
                }
            } catch (e) {
                // 忽略 URL 同步失败，不影响主流程
                console.warn('[ChatbiLayout] URL 同步失败:', e?.message || e);
            }
        };

        // 监听 activeCid 变化，统一同步 URL（不使用 immediate，避免在初始加载前清空 URL）
        watch(() => conversationStore.state.activeCid, (cid) => {
            updateUrlChatParam(cid);
        });

        return {
            // 用户信息
            userInfo,

            // 布局状态
            sidebarOpen: layoutState.sidebarOpen,
            toggleSidebar: layoutState.toggleSidebar,
            isDarkTheme: layoutState.isDarkTheme,
            handleToggleTheme: layoutState.toggleTheme,
            isDesktopView: layoutState.isDesktopView,
            windowWidth: layoutState.windowWidth,

            // 滚动相关
            userHasScrolledUp: scrollManager.userHasScrolledUp,

            // 聊天状态
            handleNewConversation,
            handleSelectConversation,  // 使用我们自己的包装函数
            handleDeleteConversation: conversationStore.openDeleteConfirmModal,
            handleShareConversation: conversationStore.shareConversation,
            // 不再中转 activeConversationId，模板直接绑定 store，避免丢失响应性
            messages: conversationStore.activeMessages,
            isLoadingMessages: computed(() => conversationStore.state.isLoading),
            messageError: computed(() => conversationStore.state.error),

            // 消息处理方法
            handleUserMessage: handleUserMessageWithLogReset,
            handleMessageError: (err) => console.error('[ChatContent] message error:', err),

            // 开发日志面板
            isDevLogVisible,
            devLogs,
            toggleDevLog,

            // Z-index 常量
            zLayoutHeader,
            zLayoutSidebar,

            
            // 暴露会话Store以供模板（ShareModal绑定等）直接访问
            conversationStore
        };
    },
    template: `
        <div class="h-screen flex flex-col min-w-0 overflow-hidden">
            <!-- Header - Fixed at top -->
            <div class="sticky top-0 z-20">
                <ChatHeader
                    :user-info="userInfo"
                    :is-dark-theme="isDarkTheme"
                    :sidebar-open="sidebarOpen"
                    :is-desktop-view="isDesktopView"
                    :active-conversation-id="conversationStore.state.activeCid"
                    :is-dev-log-visible="isDevLogVisible"
                    @toggle-theme="handleToggleTheme"
                    @toggle-sidebar="toggleSidebar"
                    @new-conversation="handleNewConversation"
                    @share-conversation="handleShareConversation"
                    @delete-conversation="handleDeleteConversation"
                    @toggle-dev-log="toggleDevLog"
                />
            </div>

            <!-- ContentContainer - 只包含侧边栏和内容区域 -->
            <ContentContainer
                :is-open="sidebarOpen"
                @toggle="toggleSidebar"
            >
                <!-- Sidebar -->
                <template #sidebar>
                    <HistorySidebar
                        :is-open="sidebarOpen"
                        :user-info="userInfo"
                        @close-sidebar="toggleSidebar"
                        @new-conversation="handleNewConversation"
                        @toggle-sidebar="toggleSidebar"
                        @select-conversation="handleSelectConversation"
                        @delete-conversation="handleDeleteConversation"
                        @share-conversation="handleShareConversation"
                    />
                </template>

                <!-- Main Content -->
                <template #mainContent>
                        <ChatContent
                            :messages="messages"
                            :is-loading-messages="isLoadingMessages"
                            :message-error="messageError"
                            :active-conversation-id="conversationStore.state.activeCid"
                            :container-class="'w-full'"
                            :is-dev-log-visible="isDevLogVisible"
                            :dev-logs="devLogs"
                            @message-sent="handleUserMessage"
                            @message-error="handleMessageError"
                            @toggle-dev-log="toggleDevLog"
                            @share-conversation="handleShareConversation"
                            @message-marked-as-bad-case="() => {}"
                        />
                </template>
            </ContentContainer>

            <!-- Modals -->
            <DeleteConfirmModal
                :is-open="conversationStore.state.deleteConfirmModal?.isOpen || false"
                :title="conversationStore.state.deleteConfirmModal?.title || '此对话'"
                @confirm="conversationStore.confirmDeleteConversation"
                @cancel="conversationStore.closeDeleteConfirmModal"
            />

            <ShareModal
                :is-open="conversationStore.state.isShareModalOpen"
                :share-url="conversationStore.state.shareUrl"
                :is-generating="conversationStore.state.isGeneratingShareLink"
                @close="conversationStore.closeShareModal"
            />
        </div>
`
};
