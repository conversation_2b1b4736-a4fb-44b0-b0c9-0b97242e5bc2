agent_name: deep_research_agent
model_provider: xm
model: kimi-k2-turbo
model_settings:
  temperature: 0.05
need_system_prompt: true
tools:
  - name: fetch_odps_sql_result
  - name: get_large_areas
  - name: fetch_ddl_for_table
  - name: get_sales_manager_team_members
# 配置在哪些工具执行后停止，允许用户查看结果
stop_at_tool_names:
  - fetch_odps_sql_result
agent_description: deep_research_agent.md
agent_tables:
  - name: app_chatbi_cust_orders_df
    desc: 客户订单分析结果表，该表是ODPS表，可使用ODPS SQL来快速获取订单数据。该表包含了近3年以来的所有订单数据，已经打平到了sku, cust_id, order_date, bd_id, warehouse_name 等维度，可以直接用于仓库维度、BD维度的分析等。只要不涉及今天以及之后的数据，都应该使用此表。
  - name: app_chatbi_mall_cust_analytics_di
    desc: 商城客户行为分析日汇总表，该表是ODPS表。按天汇总的客户行为与下单汇总，适用于天级别的客户新注册、首单统计、客户登录统计、客户转化率统计等。支持用户活跃度、留存率、转化漏斗等深度分析。
  - name: dwd_trd_saas_order_df
    desc: SaaS订单明细表，该表是ODPS表，可使用ODPS SQL来快速获取SaaS订单数据。该表包含了近3年以来的所有订单数据，已经打平到了sku, cust_id, order_date, bd_id, warehouse_name 等维度，可以直接用于仓库维度、BD维度的分析等。支持SaaS业务的深度数据挖掘。
  - name: app_chatbi_supplier_order_analysis_df
    desc: 供应商订单综合分析表，供应商订单分析结果表，整合订单、商品、仓库、供应商等维度信息，重点支持供应商维度的销售情况分析。可用于供应商绩效评估、供应链优化、采购策略制定等深度研究。
  - name: app_chatbi_search_analysis_df
    desc: 客户搜索行为分析表，客户的搜索分析汇总，以客户ID作为维度，可以用于查询搜索过特定商品的客户行为分析。支持用户意图识别、商品推荐优化、搜索算法改进等研究。
agent_as_tool_description: |
  这是一个专门用于ODPS表深度分析的AI代理，基于5个核心ODPS表提供历史数据研究服务。

  **核心分析能力：**
  - 历史订单分析：基于app_chatbi_cust_orders_df表，统计近3年订单数据，按SKU、客户ID、订单日期、BD、仓库维度进行销售分析、客户购买行为研究（不适用于今日数据）。
  - 客户行为汇总分析：基于app_chatbi_mall_cust_analytics_di表，按天汇总新注册、首单、登录、转化率等，支持活跃度、留存率、转化漏斗分析。
  - SaaS订单研究：基于dwd_trd_saas_order_df表，分析近3年SaaS订单特征、付费行为、续费模式，按SKU、客户、日期等维度。
  - 供应商订单分析：基于app_chatbi_supplier_order_analysis_df表，统计供应商销售情况、绩效评估、供应链优化、采购策略，支持订单、商品、仓库、供应商维度交叉分析。
  - 搜索行为洞察：基于app_chatbi_search_analysis_df表，以客户ID维度查询搜索特定商品的客户行为，支持意图识别、推荐优化。

  **通用数据能力：**
  - 时间序列统计：支持日、周、月、季度、年粒度趋势分析。
  - 多维度交叉：客户×商品、区域×时间、供应商×品类等OLAP查询。
  - SQL支持：复杂查询包括跨表关联、子查询、窗口函数，限于历史数据。

  该代理使用ODPS SQL工具执行查询，提供精准的历史数据洞察，支持销售管理、客户分析、供应链优化等场景。