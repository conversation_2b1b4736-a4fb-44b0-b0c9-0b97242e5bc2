# pyproject.toml
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "chatbi-mysql"
version = "0.1.0"
description = "…"
readme = "README.md"
requires-python = ">=3.9.6"
dependencies = [
    "flask>=2.0",
    "lark-oapi>=1.4.14",
    "mysql-connector-python>=8.0",
    "openai>=1.0",
    "openai-agents[litellm]>=0.2.11",
    "pandas>=2.2.3",
    "python-dotenv>=0.19",
    "pyyaml>=6.0",
    "aiohttp>=3.11.18",
    "requests>=2.25.0",
    "pyjwt>=2.8.0",
    "certifi>=2025.1.31",
    "odps>=3.5.1",
    "apscheduler>=3.10.4",
    "schedule>=1.2.2",
    "gunicorn>=21.2.0",
    "uvloop>=0.19.0",
    "httptools>=0.6.1",
    "opentelemetry-distro>=0.57b0",
    "opentelemetry-exporter-otlp>=1.36.0",
    "openpyxl>=3.1.5",
]

[project.optional-dependencies]
dev = [
    "ruff",
]

[tool.setuptools.packages.find]
# 指定在 src 下找包
where = ["src"]
include = ["parent*"]

[tool.ruff]
# 同 a black-compatible formatter.
line-length = 88
indent-width = 4

[tool.ruff.lint]
# 同 a black-compatible formatter.
select = ["E4", "E7", "E9", "F63", "F7", "F82"]

[[tool.uv.index]]
name = "tsinghua"
url = "http://mirrors.aliyun.com/pypi/simple/"
default = true
